#!/usr/bin/env python3
"""
Whisper Audio Transcriber

A simple script to transcribe audio files using OpenAI's Whisper model.
Supports various audio formats and provides multiple output options.
"""

import argparse
import os
import sys
import time
from pathlib import Path
import whisper
import json
import glob


def load_model(model_name="base"):
    """Load the Whisper model."""
    print(f"Loading Whisper model: {model_name}")
    try:
        model = whisper.load_model(model_name)
        print("Model loaded successfully!")
        return model
    except Exception as e:
        print(f"Error loading model: {e}")
        sys.exit(1)


def is_audio_file(file_path):
    """Check if file is a supported audio format."""
    supported_formats = {
        '.mp3', '.wav', '.m4a', '.aac', '.ogg', '.flac',
        '.wma', '.mp4', '.avi', '.mov', '.mkv', '.webm'
    }
    return Path(file_path).suffix.lower() in supported_formats


def expand_audio_files(file_patterns):
    """Expand file patterns and filter for audio files."""
    audio_files = []

    for pattern in file_patterns:
        if os.path.exists(pattern):
            # Direct file path
            if is_audio_file(pattern):
                audio_files.append(pattern)
            else:
                print(f"Warning: {pattern} is not a supported audio format")
        else:
            # Glob pattern
            matches = glob.glob(pattern)
            if matches:
                for match in matches:
                    if is_audio_file(match):
                        audio_files.append(match)
                    else:
                        print(f"Warning: {match} is not a supported audio format")
            else:
                print(f"Warning: No files found matching pattern: {pattern}")

    return sorted(set(audio_files))  # Remove duplicates and sort


def transcribe_audio(model, audio_path, language=None, task="transcribe"):
    """Transcribe an audio file."""
    print(f"Transcribing: {audio_path}")
    
    try:
        # Transcribe the audio
        result = model.transcribe(
            audio_path,
            language=language,
            task=task,
            verbose=True
        )
        return result
    except Exception as e:
        print(f"Error transcribing {audio_path}: {e}")
        return None


def save_transcription(result, output_path, format_type="txt"):
    """Save transcription to file in specified format."""
    try:
        if format_type == "txt":
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(result["text"])
        
        elif format_type == "json":
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
        
        elif format_type == "srt":
            # Create SRT subtitle format
            with open(output_path, 'w', encoding='utf-8') as f:
                for i, segment in enumerate(result["segments"], 1):
                    start_time = format_timestamp(segment["start"])
                    end_time = format_timestamp(segment["end"])
                    text = segment["text"].strip()
                    
                    f.write(f"{i}\n")
                    f.write(f"{start_time} --> {end_time}\n")
                    f.write(f"{text}\n\n")
        
        print(f"Transcription saved to: {output_path}")
        
    except Exception as e:
        print(f"Error saving transcription: {e}")


def format_timestamp(seconds):
    """Convert seconds to SRT timestamp format (HH:MM:SS,mmm)."""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millisecs = int((seconds % 1) * 1000)
    return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"


def get_output_path(input_path, output_dir=None, format_type="txt"):
    """Generate output file path."""
    input_path = Path(input_path)
    
    if output_dir:
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        output_path = output_dir / f"{input_path.stem}.{format_type}"
    else:
        output_path = input_path.parent / f"{input_path.stem}.{format_type}"
    
    return str(output_path)


def main():
    parser = argparse.ArgumentParser(
        description="Transcribe audio files using OpenAI Whisper",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python transcriber.py audio.mp3
  python transcriber.py audio.wav --model large --language en
  python transcriber.py audio.m4a --format json --output ./transcriptions/
  python transcriber.py *.mp3 --format srt
        """
    )
    
    parser.add_argument(
        "audio_files",
        nargs="+",
        help="Audio file(s) to transcribe"
    )
    
    parser.add_argument(
        "--model",
        default="base",
        choices=["tiny", "base", "small", "medium", "large"],
        help="Whisper model to use (default: base)"
    )
    
    parser.add_argument(
        "--language",
        help="Language of the audio (auto-detect if not specified)"
    )
    
    parser.add_argument(
        "--task",
        default="transcribe",
        choices=["transcribe", "translate"],
        help="Task to perform (default: transcribe)"
    )
    
    parser.add_argument(
        "--format",
        default="txt",
        choices=["txt", "json", "srt"],
        help="Output format (default: txt)"
    )
    
    parser.add_argument(
        "--output",
        help="Output directory (default: same as input file)"
    )
    
    args = parser.parse_args()
    
    # Load the model once
    model = load_model(args.model)

    # Expand file patterns and get audio files
    audio_files = expand_audio_files(args.audio_files)

    if not audio_files:
        print("No audio files found to process!")
        sys.exit(1)

    print(f"Found {len(audio_files)} audio file(s) to process")

    # Track processing statistics
    successful = 0
    failed = 0
    total_time = 0

    # Process each audio file
    for i, audio_file in enumerate(audio_files, 1):
        if not os.path.exists(audio_file):
            print(f"Warning: File not found: {audio_file}")
            failed += 1
            continue

        print(f"\n{'='*50}")
        print(f"Processing ({i}/{len(audio_files)}): {audio_file}")
        print(f"{'='*50}")
        
        start_time = time.time()
        
        # Transcribe the audio
        result = transcribe_audio(
            model, 
            audio_file, 
            language=args.language,
            task=args.task
        )
        
        if result:
            # Generate output path
            output_path = get_output_path(
                audio_file, 
                args.output, 
                args.format
            )
            
            # Save transcription
            save_transcription(result, output_path, args.format)
            
            # Show timing info
            elapsed_time = time.time() - start_time
            total_time += elapsed_time
            successful += 1
            print(f"Completed in {elapsed_time:.2f} seconds")

            # Show detected language if auto-detected
            if not args.language and "language" in result:
                print(f"Detected language: {result['language']}")

        else:
            print(f"Failed to transcribe: {audio_file}")
            failed += 1

    # Show final statistics
    print(f"\n{'='*50}")
    print("BATCH PROCESSING COMPLETE!")
    print(f"{'='*50}")
    print(f"Total files processed: {len(audio_files)}")
    print(f"Successful: {successful}")
    print(f"Failed: {failed}")
    print(f"Total processing time: {total_time:.2f} seconds")
    if successful > 0:
        print(f"Average time per file: {total_time/successful:.2f} seconds")
    print(f"{'='*50}")


if __name__ == "__main__":
    main()
