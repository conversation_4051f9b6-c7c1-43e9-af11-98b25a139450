# Whisper Audio Transcriber

A powerful and easy-to-use command-line tool for transcribing audio files using OpenAI's Whisper model. This tool supports multiple audio formats, batch processing, and various output formats including text, JSON, and SRT subtitles.

## Features

- 🎵 **Multiple Audio Formats**: Supports MP3, WAV, M4A, AAC, OGG, FLAC, WMA, MP4, AVI, MOV, MKV, WebM
- 🔄 **Batch Processing**: Process multiple files at once using file patterns
- 🌍 **Language Support**: Auto-detect language or specify manually
- 📝 **Multiple Output Formats**: Text, JSON (with timestamps), and SRT subtitles
- 🎯 **Model Selection**: Choose from tiny, base, small, medium, or large models
- 🔄 **Translation**: Translate audio to English
- 📊 **Progress Tracking**: Real-time progress and statistics

## Installation

1. **Clone or download this repository**
2. **Install Python dependencies**:
   ```bash
   pip3 install openai-whisper
   ```

## Quick Start

### Basic Usage

Transcribe a single audio file:
```bash
python3 transcriber.py audio.mp3
```

### Batch Processing

Transcribe multiple files:
```bash
python3 transcriber.py audio1.mp3 audio2.wav audio3.m4a
```

Use wildcards to process all audio files in a directory:
```bash
python3 transcriber.py *.mp3
python3 transcriber.py /path/to/audio/*.wav
```

### Advanced Options

Use a larger model for better accuracy:
```bash
python3 transcriber.py audio.mp3 --model large
```

Specify the language (improves speed and accuracy):
```bash
python3 transcriber.py audio.mp3 --language en
```

Generate SRT subtitles:
```bash
python3 transcriber.py video.mp4 --format srt
```

Save to a specific directory:
```bash
python3 transcriber.py *.mp3 --output ./transcriptions/
```

Translate to English:
```bash
python3 transcriber.py spanish_audio.mp3 --task translate
```

## Command Line Options

```
usage: transcriber.py [-h] [--model {tiny,base,small,medium,large}]
                      [--language LANGUAGE] [--task {transcribe,translate}]
                      [--format {txt,json,srt}] [--output OUTPUT]
                      audio_files [audio_files ...]

positional arguments:
  audio_files           Audio file(s) to transcribe

optional arguments:
  --model {tiny,base,small,medium,large}
                        Whisper model to use (default: base)
  --language LANGUAGE   Language of the audio (auto-detect if not specified)
  --task {transcribe,translate}
                        Task to perform (default: transcribe)
  --format {txt,json,srt}
                        Output format (default: txt)
  --output OUTPUT       Output directory (default: same as input file)
```

## Model Comparison

| Model  | Size | Speed | Accuracy | Use Case |
|--------|------|-------|----------|----------|
| tiny   | 39 MB | Fastest | Basic | Quick drafts, real-time |
| base   | 74 MB | Fast | Good | General use (default) |
| small  | 244 MB | Medium | Better | Higher quality needed |
| medium | 769 MB | Slow | High | Professional use |
| large  | 1550 MB | Slowest | Highest | Maximum accuracy |

## Output Formats

### Text (.txt)
Plain text transcription:
```
This is the transcribed text from your audio file.
```

### JSON (.json)
Detailed output with timestamps and metadata:
```json
{
  "text": "This is the transcribed text...",
  "segments": [
    {
      "start": 0.0,
      "end": 2.5,
      "text": "This is the transcribed text"
    }
  ],
  "language": "en"
}
```

### SRT Subtitles (.srt)
Standard subtitle format:
```
1
00:00:00,000 --> 00:00:02,500
This is the transcribed text

2
00:00:02,500 --> 00:00:05,000
from your audio file.
```

## Supported Audio Formats

- **Audio**: MP3, WAV, M4A, AAC, OGG, FLAC, WMA
- **Video**: MP4, AVI, MOV, MKV, WebM (audio track will be extracted)

## Examples

### Example 1: Basic Transcription
```bash
python3 transcriber.py interview.mp3
# Output: interview.txt
```

### Example 2: High-Quality Transcription
```bash
python3 transcriber.py lecture.wav --model large --language en
# Output: lecture.txt (using large model, English language)
```

### Example 3: Batch Processing with Subtitles
```bash
python3 transcriber.py videos/*.mp4 --format srt --output ./subtitles/
# Output: ./subtitles/video1.srt, ./subtitles/video2.srt, etc.
```

### Example 4: Translation
```bash
python3 transcriber.py french_audio.mp3 --task translate --language fr
# Output: french_audio.txt (translated to English)
```

### Example 5: Detailed JSON Output
```bash
python3 transcriber.py podcast.mp3 --format json
# Output: podcast.json (with timestamps and segments)
```

## Tips for Best Results

1. **Use the right model**: Start with `base`, upgrade to `large` for better accuracy
2. **Specify language**: If you know the language, specify it with `--language`
3. **Clean audio**: Better audio quality = better transcription
4. **Batch processing**: Process multiple files efficiently
5. **Choose format**: Use SRT for videos, JSON for detailed analysis, TXT for simple text

## Troubleshooting

### Common Issues

**"No module named 'whisper'"**
```bash
pip3 install openai-whisper
```

**"No audio files found"**
- Check file paths and extensions
- Ensure files are in supported formats

**"Model loading failed"**
- Check internet connection (models download on first use)
- Ensure sufficient disk space

**Slow processing**
- Use smaller models (tiny, base) for faster processing
- Specify language to skip auto-detection

## Performance Notes

- First run downloads the model (can take a few minutes)
- Larger models provide better accuracy but are slower
- GPU acceleration available if PyTorch with CUDA is installed
- Processing time varies: ~1-5x real-time depending on model and hardware

## License

This project uses OpenAI's Whisper model. Please refer to OpenAI's license terms for Whisper usage.
