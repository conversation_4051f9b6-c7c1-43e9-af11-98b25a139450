#!/bin/bash

# Whisper Transcriber Examples
# This script demonstrates various ways to use the transcriber

echo "=== Whisper Transcriber Examples ==="
echo

# Check if transcriber exists
if [ ! -f "transcriber.py" ]; then
    echo "Error: transcriber.py not found in current directory"
    exit 1
fi

echo "Note: These are example commands. Replace 'sample.mp3' with your actual audio files."
echo

echo "1. Basic transcription:"
echo "   python3 transcriber.py sample.mp3"
echo

echo "2. High-quality transcription with large model:"
echo "   python3 transcriber.py sample.mp3 --model large"
echo

echo "3. Specify language for better accuracy:"
echo "   python3 transcriber.py sample.mp3 --language en"
echo

echo "4. Generate SRT subtitles:"
echo "   python3 transcriber.py video.mp4 --format srt"
echo

echo "5. Batch process all MP3 files:"
echo "   python3 transcriber.py *.mp3"
echo

echo "6. Save to specific directory:"
echo "   python3 transcriber.py *.wav --output ./transcriptions/"
echo

echo "7. Translate foreign language to English:"
echo "   python3 transcriber.py spanish.mp3 --task translate"
echo

echo "8. Get detailed JSON output with timestamps:"
echo "   python3 transcriber.py interview.mp3 --format json"
echo

echo "9. Process multiple specific files:"
echo "   python3 transcriber.py file1.mp3 file2.wav file3.m4a"
echo

echo "10. Complete example with all options:"
echo "    python3 transcriber.py *.mp3 --model large --language en --format srt --output ./subs/"
echo

echo "=== Model Recommendations ==="
echo "• tiny:   Fast, basic quality - good for quick tests"
echo "• base:   Balanced speed/quality - recommended default"
echo "• small:  Better quality, slower - good for important content"
echo "• medium: High quality, slow - professional use"
echo "• large:  Best quality, slowest - maximum accuracy needed"
echo

echo "=== Supported Formats ==="
echo "Audio: mp3, wav, m4a, aac, ogg, flac, wma"
echo "Video: mp4, avi, mov, mkv, webm (extracts audio)"
echo

echo "Run 'python3 transcriber.py --help' for full documentation"
